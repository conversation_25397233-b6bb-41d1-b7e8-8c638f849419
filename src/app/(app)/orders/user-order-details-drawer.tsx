'use client';

import { useState } from 'react';

import { OrderStatusBadge } from '@/components/shared/order-status-badge';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useRootContext } from '@/root-context';
import { shouldShowSellerEarnings } from '@/services/order-service';

import { OrderDetailsBaseDrawer } from '../marketplace/order-details-drawer/order-details-base-drawer';
import { ResellOrderPriceDrawer } from '../marketplace/resell/resell-order-price-drawer';
import { CancelOrderDrawer } from './cancel-order-drawer';
import {
  UserOrderActionsSection,
  UserOrderDeadlineSection,
  UserOrderImageSection,
  UserOrderPaymentDetailsSection,
  UserOrderSellerEarningsSection,
  UserOrderStatusAlerts,
} from './user-order-details-drawer/';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType: UserType;
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { collections, currentUser } = useRootContext();
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showResellPriceDrawer, setShowResellPriceDrawer] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);

  const collection =
    collections.find((c) => c.id === order?.collectionId) || null;
  const { timeLeft, isFreezed } = useOrderTimers({ order, collection });

  const handleCancelOrder = () => setShowCancelDrawer(true);
  const handleCreateSecondaryMarketOrder = () => setShowResellPriceDrawer(true);
  // TODO: Temporarily hidden - resell tx history feature
  const handleShowResellHistory = () => {
    // setShowResellHistory(true);
  };

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleOrderResold = () => {
    onOrderUpdate();
    setShowResellPriceDrawer(false);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <>
      <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
        <div className="flex justify-center gap-3 items-center">
          <h2 className="text-xl font-bold text-[#f5f5f5]">
            Order #{order.number || order.id?.slice(-6)}
          </h2>
          <OrderStatusBadge order={order} />
        </div>

        <div className="w-[50%] mx-auto">
          <UserOrderImageSection collection={collection} />
        </div>

        <SellPriceDetails order={order} className="py-4" />

        <UserOrderPaymentDetailsSection order={order} />

        {(order.status === OrderStatus.PAID ||
          order.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
          <div className="space-y-4">
            <UserOrderDeadlineSection
              {...{
                order,
                userType,
                timeLeft,
              }}
            />
            <UserOrderStatusAlerts
              {...{
                order,
                userType,
                isFreezed,
              }}
            />
          </div>
        )}

        <OrderActors
          buyerId={order.buyerId}
          sellerId={order.sellerId}
          isOpen={open}
        />

        {shouldShowSellerEarnings(currentUser, order) && (
          <UserOrderSellerEarningsSection order={order} />
        )}

        <UserOrderActionsSection
          order={order}
          currentUserId={currentUser?.id}
          onCancelOrder={handleCancelOrder}
          onCreateSecondaryMarketOrder={handleCreateSecondaryMarketOrder}
          onShowResellHistory={handleShowResellHistory}
        />
      </OrderDetailsBaseDrawer>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={order}
        onOrderCancelled={handleOrderCancelled}
      />

      <ResellOrderPriceDrawer
        open={showResellPriceDrawer}
        onOpenChange={setShowResellPriceDrawer}
        order={order}
        onOrderResold={handleOrderResold}
      />

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </>
  );
}
