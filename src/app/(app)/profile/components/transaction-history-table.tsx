'use client';

import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useTransactionHistory } from '@/hooks/use-transaction-history';

import {
  DesktopTransactionTable,
  EmptyState,
  EndState,
  LoadingMoreState,
  LoadingState,
  MobileTransactionList,
  TransactionHeader,
} from './transaction-history';

export function TransactionHistoryTable() {
  const {
    transactions,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    handleRefresh,
    loadMore,
  } = useTransactionHistory();

  const loadMoreRef = useInfiniteScroll({
    hasMore,
    loading: loading || loadingMore,
    onLoadMore: loadMore,
  });

  if (loading) {
    return <LoadingState />;
  }

  if (transactions.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="space-y-4">
      <TransactionHeader
        onRefresh={handleRefresh}
        loading={loading}
        refreshing={refreshing}
      />

      <DesktopTransactionTable transactions={transactions} />

      <MobileTransactionList transactions={transactions} />

      <LoadingMoreState isVisible={loadingMore} />

      <EndState isVisible={!hasMore && transactions.length > 0} />

      {/* Infinite scroll trigger */}
      {hasMore && <div ref={loadMoreRef} className="h-1" />}
    </div>
  );
}
