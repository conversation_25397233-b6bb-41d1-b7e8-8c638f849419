import { RefreshCw } from 'lucide-react';

interface TransactionHeaderProps {
  onRefresh: () => void;
  loading: boolean;
  refreshing: boolean;
}

export function TransactionHeader({
  onRefresh,
  loading,
  refreshing,
}: TransactionHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-semibold text-[#f5f5f5]">
        Transaction History
      </h3>
      <button
        onClick={onRefresh}
        disabled={loading || refreshing}
        className="flex items-center gap-2 px-3 py-1.5 text-sm text-[#708499] hover:text-[#f5f5f5] transition-colors disabled:opacity-50"
      >
        <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
        Refresh
      </button>
    </div>
  );
}
