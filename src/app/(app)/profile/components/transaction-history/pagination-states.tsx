import { Loader2 } from 'lucide-react';

interface LoadingMoreStateProps {
  isVisible: boolean;
}

export function LoadingMoreState({ isVisible }: LoadingMoreStateProps) {
  if (!isVisible) return null;

  return (
    <div className="flex flex-col items-center justify-center py-6 space-y-2">
      <Loader2 className="w-6 h-6 animate-spin text-[#708499]" />
      <p className="text-[#708499] text-sm">Loading more transactions...</p>
    </div>
  );
}

interface EndStateProps {
  isVisible: boolean;
}

export function EndState({ isVisible }: EndStateProps) {
  if (!isVisible) return null;

  return (
    <div className="text-center py-4">
      <p className="text-[#708499] text-sm">
        You've reached the end of your transaction history
      </p>
    </div>
  );
}
