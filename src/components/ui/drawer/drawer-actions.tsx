'use client';

import { Loader2 } from 'lucide-react';
import type { ReactNode } from 'react';

import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { Button } from '@/components/ui/button';

interface DrawerActionsProps {
  onPrimary: () => void;
  onCancel: () => void;
  primaryLabel: string;
  primaryLoadingLabel?: string;
  cancelLabel?: string;
  primaryDisabled?: boolean;
  loading?: boolean;
  primaryClassName?: string;
  cancelClassName?: string;
  children?: ReactNode;
}

export function DrawerActions({
  onPrimary,
  onCancel,
  primaryLabel,
  primaryLoadingLabel,
  cancelLabel = 'Cancel',
  primaryDisabled = false,
  loading = false,
  primaryClassName = 'bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white',
  cancelClassName = 'border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]',
  children,
}: DrawerActionsProps) {
  return (
    <div className="flex gap-3 mt-6">
      <Button
        variant="outline"
        onClick={onCancel}
        className={`flex-1 rounded-xl py-3 font-semibold ${cancelClassName}`}
        disabled={loading}
      >
        {cancelLabel}
      </Button>
      <ConfirmWrapper>
        <Button
          onClick={onPrimary}
          disabled={primaryDisabled || loading}
          className={`flex-1 rounded-xl py-3 font-semibold disabled:opacity-50 ${primaryClassName}`}
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              {primaryLoadingLabel ?? 'Loading...'}
            </>
          ) : (
            primaryLabel
          )}
        </Button>
      </ConfirmWrapper>
      {children}
    </div>
  );
}
