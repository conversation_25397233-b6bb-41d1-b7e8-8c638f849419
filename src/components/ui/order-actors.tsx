'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';
import { useEffect, useState } from 'react';

import { getUserById } from '@/api/auth-api';
import type { UserEntity } from '@/constants/core.constants';
import { ADMIN_DEFAULT_NAME } from '@/constants/core.constants';

interface OrderActorsProps {
  buyerId?: string;
  sellerId?: string;
  isOpen: boolean;
}

interface UserInfoSectionProps {
  userId: string;
  role: 'Buyer' | 'Seller';
  isOpen: boolean;
}

function UserInfoSection({ userId, role, isOpen }: UserInfoSectionProps) {
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadUserInfo = async () => {
      if (!userId || !isOpen) {
        setUserInfo(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const user = await getUserById(userId);
        setUserInfo(user);
      } catch (error) {
        console.error(`Error loading ${role.toLowerCase()} info:`, error);
        setUserInfo(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserInfo();
  }, [userId, role, isOpen]);

  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-[#f5f5f5] text-center">
        {role} Information
      </h3>

      {loading ? (
        <div className="flex items-center justify-center gap-3 p-4 bg-[#232e3c] rounded-2xl">
          <Loader2 className="w-5 h-5 animate-spin text-[#6ab2f2]" />
          <span className="text-[#708499]">Loading user info...</span>
        </div>
      ) : userInfo ? (
        <div className="flex items-center gap-4 p-4 bg-[#232e3c] rounded-2xl">
          {userInfo.photoURL ? (
            <Avatar
              size={48}
              src={userInfo.photoURL}
              className="ring-2 ring-[#6ab2f2]/20"
            />
          ) : (
            <div className="w-12 h-12 bg-[#3a4a5c] rounded-full flex items-center justify-center ring-2 ring-[#6ab2f2]/20">
              <User className="w-6 h-6 text-[#708499]" />
            </div>
          )}
          <div className="flex-1">
            <p className="text-[#f5f5f5] font-semibold text-lg">
              {userInfo.role === 'admin'
                ? ADMIN_DEFAULT_NAME
                : userInfo.displayName || userInfo.name || 'Anonymous User'}
            </p>
            <p className="text-[#6ab2f2] text-sm font-medium">{role}</p>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-[#232e3c] rounded-2xl text-center">
          <p className="text-[#708499] text-sm">
            No {role.toLowerCase()} assigned yet
          </p>
        </div>
      )}
    </div>
  );
}

export function OrderActors({ buyerId, sellerId, isOpen }: OrderActorsProps) {
  const hasBuyer = Boolean(buyerId);
  const hasSeller = Boolean(sellerId);

  if (!hasBuyer && !hasSeller) {
    return null;
  }

  return (
    <div className="space-y-4">
      {hasBuyer && (
        <UserInfoSection userId={buyerId!} role="Buyer" isOpen={isOpen} />
      )}
      {hasSeller && (
        <UserInfoSection userId={sellerId!} role="Seller" isOpen={isOpen} />
      )}
    </div>
  );
}
