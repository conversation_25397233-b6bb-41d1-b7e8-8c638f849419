'use client';

import { Caption } from '@telegram-apps/telegram-ui';

import { CollectionName } from '@/components/shared/collection-name';
import { OrderImage } from '@/components/shared/order-image';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { SellButtonComponent } from '@/components/shared/sell-button-component';
import { Card, CardContent } from '@/components/ui/card';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

interface BaseOrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  animated?: boolean;
  children?: React.ReactNode;
  imageBadge?: React.ReactNode;
  // New props for marketplace functionality
  showButton?: boolean;
  buttonLabel?: string;
  activeTab?: 'buyers' | 'sellers';
}

export function BaseOrderCard({
  order,
  collection,
  onClick,
  children,
  imageBadge,
  showButton = false,
  buttonLabel,
  activeTab,
}: BaseOrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);
  const finalButtonLabel =
    buttonLabel || (activeTab === 'sellers' ? 'Fulfill' : 'Buy');
  const finalImageBadge =
    imageBadge || (isSecondary && <SecondaryMarketBadge />);
  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col">
        <div className="relative">
          <OrderImage order={order} collection={collection} />
          <div className="flex w-full items-center justify-start mb-1 absolute top-0 left-0 px-2 pt-1.5">
            {finalImageBadge}
          </div>
        </div>

        <div className="flex items-center justify-between mt-2 mb-2">
          <Caption level="1" weight="1" className="truncate">
            <CollectionName collection={collection} />
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ??
              (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
          </Caption>
        </div>

        {showButton ? (
          <SellButtonComponent order={order} label={finalButtonLabel} />
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}
