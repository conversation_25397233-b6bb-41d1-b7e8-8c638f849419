import * as admin from "firebase-admin";
import {
  APP_CONFIG_COLLECTION,
  APP_CONFIG_DOC_ID,
  BPS_DIVISOR,
  MARKETPLACE_REVENUE_USER_ID,
} from "../constants";
import { AppConfigEntity, UserEntity, TxType } from "../types";
import { safeDivide, safeMultiply, safeSubtract } from "../utils";
import {
  addFunds,
  addFundsWithHistory,
  updateUserBalance,
  validateSufficientFunds,
} from "./balance-service";
import { createTransactionRecord } from "./transaction-history-service";
import { log } from "../utils/logger";

export async function getAppConfig() {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .get();

    if (!doc.exists) {
      log.warn("App config not found, using zero fees", {
        operation: "app_config_fetch",
      });
      return {
        deposit_fee: 0,
        withdrawal_fee: 0,
        referrer_fee: 0,
        cancel_order_fee: 0,
        purchase_fee: 0,
        buyer_lock_percentage: 0,
        seller_lock_percentage: 0,
        resell_purchase_fee: 0,
        resell_purchase_fee_for_seller: 0,
        min_deposit_amount: 0,
        min_withdrawal_amount: 0,
        max_withdrawal_amount: 0,
        min_secondary_market_price: 0,
        fixed_cancel_order_fee: 0,
      };
    }

    return doc.data() as AppConfigEntity;
  } catch (error) {
    log.error("Error getting app config", error, {
      operation: "app_config_fetch",
    });
    throw error;
  }
}

export function calculateFeeAmount(amount: number, feeBps: number) {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return safeDivide(safeMultiply(amount, feeBps), BPS_DIVISOR);
}

export async function getAdminUser() {
  try {
    const db = admin.firestore();
    const adminQuery = await db
      .collection("users")
      .where("role", "==", "admin")
      .limit(1)
      .get();

    if (adminQuery.empty) {
      log.error("No admin user found", undefined, {
        operation: "admin_user_lookup",
      });
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    log.error("Error finding admin user", error, {
      operation: "admin_user_lookup",
    });
    throw error;
  }
}

export async function applyFeeToMarketplaceRevenue(params: {
  feeAmount: number;
  feeType: string;
}) {
  const { feeAmount, feeType } = params;
  if (feeAmount <= 0) {
    return;
  }

  try {
    await addFunds(MARKETPLACE_REVENUE_USER_ID, feeAmount);
    log.feeLog("Applied fee to marketplace revenue", {
      feeAmount,
      feeType,
      userId: MARKETPLACE_REVENUE_USER_ID,
    });
  } catch (error) {
    log.error("Error applying fee to marketplace revenue", error, {
      feeType,
      feeAmount,
      operation: "marketplace_revenue",
    });
    throw error;
  }
}

export async function applyDepositFee(params: { depositAmount: number }) {
  const { depositAmount } = params;
  try {
    const config = await getAppConfig();
    if (!config?.deposit_fee) {
      return depositAmount;
    }

    // Deposit fee is now a static TON value, not BPS
    const feeAmount = config.deposit_fee;
    if (feeAmount <= 0 || feeAmount >= depositAmount) {
      return depositAmount;
    }

    const netAmount = safeSubtract(depositAmount, feeAmount);

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "deposit",
    });

    log.feeLog("Deposit fee applied", {
      feeAmount,
      feeType: "deposit",
      netAmount,
    });

    return netAmount;
  } catch (error) {
    log.error("Error applying deposit fee", error, {
      operation: "deposit_fee",
      depositAmount,
    });
    throw error;
  }
}

export async function applyFixedCancelOrderFee(userId: string) {
  try {
    const config = await getAppConfig();
    if (!config?.fixed_cancel_order_fee) {
      return 0;
    }

    // Fixed cancel order fee is a static TON value, not BPS
    const feeAmount = config.fixed_cancel_order_fee;
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "fixed cancel order fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    // Record transaction history for penalty payment
    await createTransactionRecord({
      userId,
      txType: TxType.CANCELATION_FEE,
      amount: feeAmount,
      description: `Fixed cancellation fee penalty (${feeAmount} TON)`,
      isReceivingCompensation: false,
    });

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "fixed_cancel_order",
    });

    log.feeLog("Fixed cancel order fee applied", {
      feeAmount,
      feeType: "fixed_cancel_order",
      userId,
    });

    return feeAmount;
  } catch (error) {
    log.error("Error applying fixed cancel order fee", error, {
      operation: "fixed_cancel_order_fee",
      userId,
    });
    throw error;
  }
}

export async function applyResellPurchaseFeeWithReferral(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  resellPurchaseFeeBPS: number;
  referrerFeeBPS: number;
}) {
  const { buyerId, amount, referralId, resellPurchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!resellPurchaseFeeBPS || resellPurchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, resellPurchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "resell purchase fee with referral",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          log.feeLog("Using custom referral fee", {
            referrerFeeRate,
            referrerId,
            feeType: "custom_referral",
            feeAmount: 0,
          });
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          log.feeLog("Using order referral fee", {
            referrerFeeRate,
            referrerId,
            feeType: "order_referral",
            feeAmount: 0,
          });
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFundsWithHistory({
            userId: referrerId,
            amount: referralFeeAmount,
            txType: TxType.REFERRAL_FEE,
            description: `Referral fee from resell purchase (${referralFeeAmount} TON)`,
          });

          log.feeLog("Resell referral fee applied", {
            feeAmount: referralFeeAmount,
            feeType: "resell_referral",
            referrerFeeRate,
            referrerId,
            referralId,
          });
        }
      } else {
        log.warn(
          "Referrer with tg_id not found, adding full fee to marketplace",
          {
            referralId,
            operation: "resell_referral_lookup",
          }
        );
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFeeAmount,
        feeType: "resell_purchase",
      });
    }

    log.feeLog("Resell purchase fee with referral applied", {
      feeAmount: totalFeeAmount,
      feeType: "resell_purchase_total",
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    log.error("Error applying resell purchase fee with referral", error, {
      operation: "resell_purchase_fee",
    });
    throw error;
  }
}

export async function applyPurchaseFeeWithReferralFromOrder(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  purchaseFeeBPS: number;
  referrerFeeBPS: number;
}) {
  const { buyerId, amount, referralId, purchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!purchaseFeeBPS || purchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "purchase fee with referral from order",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          log.feeLog("Using custom referral fee", {
            referrerFeeRate,
            referrerId,
            feeType: "custom_referral",
            feeAmount: 0,
          });
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          log.feeLog("Using order referral fee", {
            referrerFeeRate,
            referrerId,
            feeType: "order_referral",
            feeAmount: 0,
          });
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFundsWithHistory({
            userId: referrerId,
            amount: referralFeeAmount,
            txType: TxType.REFERRAL_FEE,
            description: `Referral fee from purchase (${referralFeeAmount} TON)`,
          });

          log.feeLog("Purchase referral fee applied", {
            feeAmount: referralFeeAmount,
            feeType: "purchase_referral",
            referrerFeeRate,
            referrerId,
            referralId,
          });
        }
      } else {
        log.warn(
          "Referrer with tg_id not found, adding full fee to marketplace",
          {
            referralId,
            operation: "purchase_referral_lookup",
          }
        );
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFeeAmount,
        feeType: "purchase",
      });
    }

    log.feeLog("Purchase fee with referral from order applied", {
      feeAmount: totalFeeAmount,
      feeType: "purchase_total",
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    log.error("Error applying purchase fee with referral from order", error, {
      operation: "purchase_fee_with_referral",
    });
    throw error;
  }
}

export async function applyWithdrawFee(userId: string, withdrawAmount: number) {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawal_fee) {
      return 0;
    }

    // Withdraw fee is now a static TON value, not BPS
    const feeAmount = config.withdrawal_fee;
    if (feeAmount <= 0 || feeAmount >= withdrawAmount) {
      return 0;
    }

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "withdrawal",
    });

    log.feeLog("Withdrawal fee applied", {
      feeAmount,
      feeType: "withdrawal",
      userId,
    });

    return feeAmount;
  } catch (error) {
    log.error("Error applying withdrawal fee", error, {
      operation: "withdrawal_fee",
      userId,
      withdrawAmount,
    });
    throw error;
  }
}
